<template>
  <div class="qrcode-login-container">
    <div class="qrcode-status" v-if="!qrcodeExpired">
      <div class="qrcode-wrapper">
        <img v-if="qrcodeUrl" :src="qrcodeUrl" alt="登录二维码" class="qrcode-image" />
        <div v-else class="qrcode-loading">
          <QrcodeOutlined class="qrcode-icon" />
          <p class="qrcode-text">正在生成二维码...</p>
        </div>
      </div>
      <p class="qrcode-tip">请使用小程序扫码登录</p>
    </div>
    <div class="qrcode-expired" v-else>
      <QrcodeOutlined class="qrcode-expired-icon" />
      <p class="qrcode-expired-text">二维码已过期</p>
      <a-button type="primary" @click="refreshQrcode" :loading="qrcodeLoading">
        <ReloadOutlined />
        刷新二维码
      </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue';
import { QrcodeOutlined, ReloadOutlined } from '@geega-ui-plus/icons-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  V1OpenApiUserLoginScanLoginStatus,
  V1OpenApiUserLoginScanQrcode
} from '/@/api/cddc.req';
import { toDataURL } from '/@/utils/qrcode';

interface QrcodeLoginEmits {
  (e: 'loginSuccess', userInfo: any): void;
}

interface Props {
  deviceIp?: string;
}

const props = withDefaults(defineProps<Props>(), {
  deviceIp: '',
});

const emit = defineEmits<QrcodeLoginEmits>();

const { createMessage } = useMessage();

// 扫码登录相关状态
const qrcodeUrl = ref<string>('');
const qrcodeToken = ref<string>('');
const qrcodeExpired = ref(false);
const qrcodeLoading = ref(false);
const pollingTimer = ref<NodeJS.Timeout | null>(null);
const qrcodeTimer = ref<NodeJS.Timeout | null>(null);
const isPolling = ref(false); // 添加轮询状态标志

// 生成二维码
const generateQrcode = async () => {
  try {
    // 调用后端接口获取token
    const response = await V1OpenApiUserLoginScanQrcode();

    if (response) {
      // 后端直接返回token字符串
      qrcodeToken.value = response;

      // 构造二维码内容，包含token和其他必要信息
      const qrcodeData = JSON.stringify({
        token: qrcodeToken.value,
        type: 'login',
        deviceIp: props.deviceIp,
        timestamp: Date.now()
      });

      // 使用qrcode库生成二维码图片
      const qrcodeDataUrl = await toDataURL(qrcodeData, {
        width: 200,
        margin: 1,
        color: {
          dark: '#00996b',
          light: '#FFFFFF'
        }
      });

      qrcodeUrl.value = qrcodeDataUrl;
      qrcodeExpired.value = false;

      // 设置二维码过期时间（5分钟）
      qrcodeTimer.value = setTimeout(() => {
        qrcodeExpired.value = true;
        stopPolling();
        // 清理二维码URL
        qrcodeUrl.value = '';
      }, 5 * 60 * 1000);

    } else {
      throw new Error('获取登录token失败');
    }

  } catch (error) {
    console.error('生成二维码失败:', error);
    createMessage.error('生成二维码失败');
    throw error;
  }
};

// 开始轮询登录状态
const startPolling = () => {
  stopPolling(); // 先停止之前的轮询
  isPolling.value = true; // 设置轮询状态

  const poll = async () => {
    try {
      // 检查轮询状态，如果已停止则不继续
      if (!isPolling.value || qrcodeExpired.value) {
        return;
      }

      const response = await V1OpenApiUserLoginScanLoginStatus({
        token: qrcodeToken.value
      });

      // 检查登录状态
      if (response) {
        const loginData = response as any;

        // 检查登录状态
        if (loginData.status === 'CONFIRMED') {
          // 登录确认成功，直接使用已获得的token完成登录
          const authToken = loginData.authToken;

          if (authToken && authToken.token) {
            // 扫码登录已完成，直接触发登录成功事件
            // 传递完整的认证信息，让父组件直接完成登录流程
            emit('loginSuccess', {
              // 扫码登录成功，直接使用token信息
              token: authToken.token,
              refreshToken: authToken.refreshToken,
              expiresIn: authToken.expiresIn,
              loginType: 5, // 扫码登录类型
            });
            stopPolling();
            return;
          }
        }

        // 检查二维码过期状态
        if (loginData.status === 'EXPIRED') {
          qrcodeExpired.value = true;
          stopPolling();
          return;
        }

        // 如果是等待扫码或已扫码待确认状态，继续轮询
        if (loginData.status === 'WAITING' || loginData.status === 'SCANNED') {
          // 继续轮询
        }
      }

      // 继续轮询
      if (isPolling.value && !qrcodeExpired.value) {
        pollingTimer.value = setTimeout(poll, 3000); // 3秒轮询一次
      }
    } catch (error) {
      console.error('轮询登录状态失败:', error);
      // 出错时继续轮询，但增加间隔时间
      if (isPolling.value && !qrcodeExpired.value) {
        pollingTimer.value = setTimeout(poll, 5000); // 出错时5秒后重试
      }
    }
  };

  // 立即开始第一次轮询
  poll();
};

// 停止轮询
const stopPolling = () => {
  isPolling.value = false; // 设置轮询状态为停止
  if (pollingTimer.value) {
    clearTimeout(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 刷新二维码
const refreshQrcode = async () => {
  try {
    qrcodeLoading.value = true;
    stopPolling();
    await generateQrcode();
    startPolling();
  } catch (error) {
    console.error('刷新二维码失败:', error);
    createMessage.error('刷新二维码失败');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 清理扫码登录相关资源
const cleanupQrcodeLogin = () => {
  stopPolling();
  if (qrcodeTimer.value) {
    clearTimeout(qrcodeTimer.value);
    qrcodeTimer.value = null;
  }
  // 清理二维码URL和状态
  qrcodeUrl.value = '';
  qrcodeToken.value = '';
  qrcodeExpired.value = false;
  isPolling.value = false; // 确保轮询状态被重置
};

// 初始化扫码登录
const initQrcodeLogin = async () => {
  try {
    qrcodeLoading.value = true;
    await generateQrcode();
    startPolling();
  } catch (error) {
    console.error('初始化扫码登录失败:', error);
    createMessage.error('初始化扫码登录失败');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  initQrcodeLogin,
  cleanupQrcodeWebSocket: cleanupQrcodeLogin,
});

// 组件卸载时清理
onUnmounted(() => {
  cleanupQrcodeLogin();
});
</script>

<style lang="less" scoped>
.qrcode-login-container {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;

  .qrcode-status {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background: rgba(0, 153, 107, 0.1);
    border: 1px solid rgba(0, 153, 107, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;

    .qrcode-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 10px;

      .qrcode-image {
        width: 160px;
        height: 160px;
        border-radius: 8px;
        background: #ffffff;
        padding: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .qrcode-loading {
        width: 160px;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(0, 153, 107, 0.2);
        border-radius: 8px;

        .qrcode-icon {
          font-size: 48px;
          color: #00996b;
          margin-bottom: 16px;
          animation: pulse 1.5s infinite;
          filter: drop-shadow(0 0 8px rgba(0, 153, 107, 0.3));
        }

        .qrcode-text {
          margin: 0;
          color: rgba(255, 255, 255, 0.85);
          font-size: 16px;
        }
      }
    }

    .qrcode-tip {
      margin: 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 14px;
      text-align: center;
    }
  }

  .qrcode-expired {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;

    .qrcode-expired-icon {
      font-size: 48px;
      color: rgba(255, 255, 255, 0.45);
      margin-bottom: 16px;
    }

    .qrcode-expired-text {
      margin: 0 0 24px 0;
      color: rgba(255, 255, 255, 0.65);
      font-size: 16px;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(0, 153, 107, 0.4));
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
