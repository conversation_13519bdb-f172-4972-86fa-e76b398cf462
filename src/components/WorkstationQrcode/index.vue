<template>
  <div class="workstation-qrcode">
    <a-button type="text" @click="showQrcode" class="qrcode-btn">
      <template #icon>
        <QrcodeOutlined />
      </template>
      工位信息
    </a-button>

    <!-- 二维码弹窗 -->
    <BasicModal
      @register="register"
      title="工位信息二维码"
      width="400px"
      centered
      destroyOnClose
      :showCancelBtn="false"
      ok-text="关闭"
      @ok="openModal(false)"
    >
      <div class="qrcode-content">
        <div class="qrcode-wrapper">
          <img v-if="qrcodeUrl" :src="qrcodeUrl" alt="工位信息二维码" class="qrcode-image" />
          <div v-else class="qrcode-loading">
            <a-spin size="large" />
            <p class="loading-text">正在生成二维码...</p>
          </div>
        </div>
        <div class="qrcode-info">
          <p class="qrcode-tip">请使用小程序扫码查看工位信息和学习视频</p>
          <div class="workstation-details" v-if="workstationInfo?.name">
            <div class="detail-item">
              <span class="label">工位名称：</span>
              <span class="value">{{ workstationInfo.levelName }}-{{ workstationInfo.name }}</span>
            </div>
            <div class="detail-item" v-if="userInfo?.name">
              <span class="label">当前用户：</span>
              <span class="value">{{ userInfo.name }}</span>
            </div>
            <div class="detail-item" v-if="currentProject?.name">
              <span class="label">当前项目：</span>
              <span class="value">{{ currentProject.name }}</span>
            </div>
          </div>
        </div>
        <div class="qrcode-actions">
          <a-button @click="refreshQrcode" :loading="qrcodeLoading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新二维码
          </a-button>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { QrcodeOutlined, ReloadOutlined } from '@geega-ui-plus/icons-vue';
import { message } from '@geega-ui-plus/ant-design-vue';
import { BasicModal, useModal } from '@geega-ui-plus/geega-ui';
import { toDataURL } from '/@/utils/qrcode';

interface WorkstationInfo {
  id?: string;
  name?: string;
  levelName?: string;
  locationId?: string;
  [key: string]: any;
}

interface UserInfo {
  id?: string;
  name?: string;
  [key: string]: any;
}

interface Props {
  workstationInfo?: WorkstationInfo;
  userInfo?: UserInfo;
  currentProject?: any;
}

const props = withDefaults(defineProps<Props>(), {
  workstationInfo: () => ({}),
  userInfo: () => ({}),
  currentProject: () => ({}),
});

const [register, { openModal }] = useModal();
const qrcodeUrl = ref('');
const qrcodeLoading = ref(false);

// 显示二维码弹窗
const showQrcode = async () => {
  openModal(true);
  await generateQrcode();
};

// 生成二维码
const generateQrcode = async () => {
  try {
    qrcodeLoading.value = true;
    qrcodeUrl.value = '';

    // 构造二维码数据
    const qrcodeData = JSON.stringify({
      type: 'workstation_info',
      workstationId: props.workstationInfo?.locationId || props.workstationInfo?.id,
      workstationName: props.workstationInfo?.name,
      levelName: props.workstationInfo?.levelName,
      userId: props.userInfo?.id,
      userName: props.userInfo?.name,
      // 添加项目信息，包含学习视频所需的countAlgorithm和videoUrl
      projectId: props.currentProject?.projectId,
      projectName: props.currentProject?.name,
      countAlgorithm: props.currentProject?.countAlgorithm,
      videoUrl: props.currentProject?.videoUrl,
      // 添加更多工位相关信息
      terminalInfo: props.workstationInfo?.terminalDTO || props.workstationInfo?.terminal,
      timestamp: Date.now(),
      // 标识这是来自训练系统的二维码
      source: 'cddc_training_system',
      version: '1.0'
    });

    // 生成二维码图片
    const qrcodeDataUrl = await toDataURL(qrcodeData, {
      width: 200,
      margin: 2,
      color: {
        dark: '#1890ff',
        light: '#FFFFFF'
      }
    });

    qrcodeUrl.value = qrcodeDataUrl;
  } catch (error) {
    console.error('生成二维码失败:', error);
    message.error('生成二维码失败，请重试');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 刷新二维码
const refreshQrcode = async () => {
  await generateQrcode();
};
</script>

<style lang="less" scoped>
.workstation-qrcode {
  .qrcode-btn {
    color: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 6px 12px;
    height: auto;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      color: #1890ff;
      background-color: rgba(255, 255, 255, 0.1);
      border-color: #1890ff;
    }

    .anticon {
      margin-right: 6px;
      font-size: 16px;
    }
  }
}

.qrcode-content {
  text-align: center;

  .qrcode-wrapper {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .qrcode-image {
      width: 200px;
      height: 200px;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
    }

    .qrcode-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .loading-text {
        margin: 0;
        color: #666;
      }
    }
  }

  .qrcode-info {
    margin-bottom: 16px;

    .qrcode-tip {
      color: #666;
      font-size: 14px;
      margin-bottom: 12px;
    }

    .workstation-details {
      text-align: left;
      background: #f5f5f5;
      padding: 12px;
      border-radius: 6px;

      .detail-item {
        display: flex;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          min-width: 80px;
        }

        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }

  .qrcode-actions {
    display: flex;
    justify-content: center;
  }
}
</style>
