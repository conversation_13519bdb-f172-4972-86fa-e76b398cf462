import { defHttp } from '../utils/http/axios';

interface APIParameters {
  url: string;
  /**
   * request body
   */
  data?: any;
  /**
   * url path param
   */
  param?: any;
  /**
   * query
   */
  query?: any;
}

export const httpAdaptor = {
  post<T>(data: APIParameters) {
    return defHttp.post<ResponseWrapper<T>>({
      url: data.url,
      params: data.data,
      headers: {
        ignoreCancelToken: true,
      },
    });
  },
  get<T>(data: APIParameters) {
    return defHttp.get<ResponseWrapper<T>>({ url: data.url, params: data.query });
  },
  getStream<T>(data: APIParameters) {
    return defHttp.getStream<T>({
      url: data.url,
      params: data.query,
    });
  },
  put<T>(data: APIParameters) {
    return defHttp.put<ResponseWrapper<T>>({
      url: data.url,
      params: data.data,
      headers: {
        ignoreCancelToken: true,
      },
    });
  },
  delete<T>(data: APIParameters) {
    return defHttp.delete<ResponseWrapper<T>>({
      url: data.url,
      params: data.query,
      headers: {
        ignoreCancelToken: true,
      },
    });
  },
  patch<T>(data: APIParameters) {
    throw new Error('Not implement');
  },
};

export function handleParams(params: any, opt?: { isData?: boolean; isPage?: boolean }) {
  return params;
}

export type BodyWrapper<T> = T extends { data?: any } ? T & T['data'] : T;

export type ResponseWrapper<T> = Required<DeconstructResponseType<T>>;

export type Required<T> = [T] extends [infer U | undefined | null] ? U : T;

export type DeconstructResponseType<T> = T extends { total?: any }
  ? T
  : T extends { data?: any }
  ? T['data']
  : T extends { result?: any }
  ? T['result']
  : T;
